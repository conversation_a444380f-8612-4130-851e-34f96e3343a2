<style>
  .cat__menu-left__item--hidden {
    display: none !important;
  }

  .cat__menu-left__item--disabled {
    cursor: not-allowed !important;
  }

  .cat__menu-left__item--disabled > a {
    pointer-events: none !important;
  }
  .cat__menu-left__item--disabled > ul {
    pointer-events: none !important;
    display: none !important;
  }

  .toggleMenuClick {
    -webkit-transform: translate3d(-14.29rem, 0, 0);
    transform: translate3d(-14.29rem, 0, 0);
  }
  .cat__menu-left:hover {
    transition: 0.2s ease-out 1s;
    transform: translate3d(0, 0, 0);
  }
</style>
<nav class="cat__menu-left">
  <div class="cat__menu-left__lock cat__menu-left__action--menu-toggle">
    <div class="cat__menu-left__pin-button">
      <div>
        <!-- -->
      </div>
    </div>
  </div>
  <div class="cat__menu-left__logo">
    <a [routerLink]="[userData && userData.defaultPage]">
      <img *ngIf="!showTenantLogo" [src]="imgUrl" onerror="this.src='assets/modules/dummy-assets/common/img/account-logo-on-white-bg.png'" />
      <img
        *ngIf="showTenantLogo"
        src="{{ sharedService.assetsUrl }}/a/{{ userData.crossTenantId }}/img/account-logo-on-white-bg.png"
        onerror="this.src='assets/modules/dummy-assets/common/img/account-logo-on-white-bg.png'" />
    </a>
  </div>

  <div class="cat__menu-left__inner" *ngIf="menuShow">
    <ul class="cat__menu-left__list cat__menu-left__list--root">
      <!-- <li [hidden]="userData.group != 3 || (userData.group == 3 && patientTopic.count =='0')" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                <a [routerLink]="['faq']">
                    <span class="cat__menu-left__icon"><i class="fa fa-question" aria-hidden="true"></i></span> Frequently Asked Questions
                </a>
            </li> -->
      <li
        class="cat__menu-left__item cat__menu-left__submenu"
        [ngClass]="
          manageConfig.enable_message_center != 1
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        ">
        <!--  [ngClass]="(userData.group != 3 || patientTopic.count =='0')?'cat__menu-left--colorful--secondary cat__menu-left__submenu--toggled':''" -->
        <a class="inbox-collapse-count" href="javascript: void(0);" id="message_center">
          <span class="cat__menu-left__icon icmn-bubbles"></span> Message Center
          <span class="badge badge-danger pull-right" [hidden]="!inboxCountLoading || inboxLoadingError"
            ><img src="./assets/img/loader/color.gif" class="menu-spinner"
          /></span>
          <!-- <span class="badge badge-danger pull-right left-menu-unread-count" [hidden]="inboxCountLoading || inboxLoadingError || !((inboxData |  unreadmessageFilter) + CSRMessages.length)">{{(inboxData |  unreadmessageFilter) + CSRMessages.length}}</span> -->
          <span
            class="badge badge-danger pull-right left-menu-unread-count"
            [hidden]="
              inboxCountLoading ||
              inboxLoadingError ||
              !(inboxUnreadMessageCount + CSRMessages.length) ||
              inboxUnreadMessageCount + CSRMessages.length < 1
            "
            >{{ inboxUnreadMessageCount + CSRMessages.length }}</span
          >
        </a>
        <ul class="cat__menu-left__list remove-ul">
          <!--  [style.display]="(userData.group != 3 || patientTopic.count =='0') ? 'block' : null" -->
          <!-- Hide cases
                        1. if user has no tenant
                        2. -->
          <li
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']"
            [hidden]="
              !userData.tenantId ||
              ((+userData.group === 3 || (!previlages.chatWithClinician && !previlages.chatWithPatients && !previlages.chatWithMessageGroups)) &&
                (+userData.group !== 3 || !previlages.chatWithClinician))
            "
          >
            <a (click)="chatWithModel(userData.group)" id="new_message"> <span class="cat__menu-left__icon icmn-bubbles4"></span> New Message </a>
          </li>
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['inbox']" id="active_messages" (click)="linkToInbox($event)">
              <span class="cat__menu-left__icon icmn-bubbles4"></span> Active Messages
              <span class="badge badge-danger pull-right" [hidden]="!inboxCountLoading || inboxLoadingError"
                ><img src="./assets/img/loader/color.gif" class="menu-spinner"
              /></span>
              <!-- <span class="badge badge-danger pull-right left-menu-unread-count" [hidden]="inboxCountLoading || inboxLoadingError || !((inboxData |  unreadmessageFilter) + CSRMessages.length)">{{(inboxData |  unreadmessageFilter) + CSRMessages.length}}</span> -->
              <span
                class="badge badge-danger pull-right left-menu-unread-count"
                [hidden]="
                  inboxCountLoading ||
                  inboxLoadingError ||
                  !(inboxUnreadMessageCount + CSRMessages.length) ||
                  inboxUnreadMessageCount + CSRMessages.length < 1
                "
                >{{ inboxUnreadMessageCount + CSRMessages.length }}</span
              >
            </a>
          </li>
          <li
            [hidden]="
              !this.menuSettings.allowPatientDiscussionGroup || (!previlages.superAdmin && !previlages.manageAllMessageGroup) || !canNaUserManageGroup
            "
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['message/patient-discussion-groups']" id="create_edit_patient_discussion_groups">
              <span class="cat__menu-left__icon icmn-bubbles3"></span> Patient Discussion Groups
            </a>
          </li>
          <li
            [hidden]="(!previlages.superAdmin && !previlages.manageAllMessageGroup) || !canNaUserManageGroup"
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['message/message']" id="create_edit_message_groups">
              <span class="cat__menu-left__icon icmn-bubbles4"></span> Create/Edit Message Groups
            </a>
          </li>
          <li
            [hidden]="!previlages.superAdmin && !previlages.sendBroadcaseMessage"
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['message/message-broadcast']" id="send_broadcast_messages">
              <span class="cat__menu-left__icon icmn-podcast"></span> Send Broadcast Messages
            </a>
          </li>
          <li
            [hidden]="!previlages.superAdmin && !previlages.sendMaskedMessage"
            class="cat__menu-left__item"
            [ngClass]="
              manageConfig?.enable_masked_discussion_group != 1
                ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
                  ? 'cat__menu-left__item--disabled'
                  : 'cat__menu-left__item--hidden'
                : ''
            "
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['masked/message']" id="send_masked_messages">
              <span class="cat__menu-left__icon icmn-bubbles3"></span> Send Masked Messages
            </a>
          </li>
          <li
            [hidden]="!previlages.superAdmin && !previlages.showArchive"
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['archive']" id="archived_messages">
              <span class="cat__menu-left__icon"><i class="icmn-box-add"></i></span> Archived Messages
            </a>
          </li>
        </ul>
      </li>

      <!--  Manage Alert Center starts -->
      <!-- <li [hidden]="(!previlages.superAdmin && !previlages.showManageAlert)"
            [ngClass]="((manageConfig.show_manage_alerts != 1 )? ((manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1) ? 'cat__menu-left__item--disabled' : 'cat__menu-left__item--hidden') : '')"
            class="cat__menu-left__item">
             <a class="inbox-collapse-count" [routerLink]="['/banner-alerts']" id="manage_alert_center">
              <span class="cat__menu-left__icon"><img
                style="width:16px;" src="./assets/img/alert.png" /></span> Manage Alert Center
                 </a>
            </li> -->

      <li
        [ngClass]="
          manageConfig.show_document_tagging != 1
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        "
        class="cat__menu-left__item cat__menu-left__submenu">
        <a class="inbox-collapse-count" href="javascript: void(0);" id="document_center">
          <!-- <span class="cat__menu-left__icon icmn-bubbles"></span>--><span class="cat__menu-left__icon"
            ><img style="width: 16px" src="./assets/img/document-center.png"
          /></span>
          Document Center
          <span class="badge badge-danger pull-right" *ngIf="signatureCountLoading && !signatureLoadingError"
            ><img src="./assets/img/loader/color.gif" class="menu-spinner"
          /></span>
          <span
            class="badge badge-danger pull-right left-menu-unread-count"
            *ngIf="
              !structureService.signatureListServerSidePagination &&
              !signatureCountLoading &&
              !signatureLoadingError &&
              (signatureRequest | signatureRequestUnreadCount: userData.userId)
            "
            >{{ signatureRequest | signatureRequestUnreadCount: userData.userId }}</span
          >

          <span
            class="badge badge-danger pull-right left-menu-unread-count"
            *ngIf="
              structureService.signatureListServerSidePagination &&
              !signatureCountLoading &&
              !signatureLoadingError &&
              structureService.signatureRequestUnreadCount > 0
            ">
            {{ structureService.signatureRequestUnreadCount }}
          </span>
        </a>
        <ul class="cat__menu-left__list">
          <li
            [hidden]="nursingAgencyUser"
            *ngIf="showNewSignature"
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['/signature/new-signature']" id="signature_requests">
              <span class="cat__menu-left__icon icmn-pen"></span> Signature Requests
            </a>
          </li>
          <li
            class="cat__menu-left__item"
            *ngIf="(!previlages.viewAllSignedDocs && !previlages.manageTenants) || nursingAgencyUser || userData.accessSecurityEnabled"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['signature/signature-requests-list']" id="signature_worklists_two">
              <span class="cat__menu-left__icon icmn-pen"></span> Signature Worklists
              <span class="badge badge-danger pull-right" *ngIf="signatureCountLoading && !signatureLoadingError"
                ><img src="assets/img/loader/color.gif" class="menu-spinner"
              /></span>
              <span
                class="badge badge-danger pull-right"
                *ngIf="
                  !structureService.signatureListServerSidePagination &&
                  !signatureCountLoading &&
                  !signatureLoadingError &&
                  (signatureRequest | signatureRequestUnreadCount: userData.userId)
                "
                >{{ signatureRequest | signatureRequestUnreadCount: userData.userId }}</span
              >
              <span
                class="badge badge-danger pull-right left-menu-unread-count"
                *ngIf="
                  structureService.signatureListServerSidePagination &&
                  !signatureCountLoading &&
                  !signatureLoadingError &&
                  structureService.signatureRequestUnreadCount > 0
                ">
                {{ structureService.signatureRequestUnreadCount }}
              </span>
            </a>
          </li>
          <li
            class="cat__menu-left__item"
            *ngIf="(previlages.viewAllSignedDocs || previlages.manageTenants) && !nursingAgencyUser && !userData.accessSecurityEnabled"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['signature/signed-documents-list']" id="signature_worklists_four">
              <span class="cat__menu-left__icon icmn-pen"></span> Signature Worklists
              <span class="badge badge-danger pull-right" *ngIf="signatureCountLoading && !signatureLoadingError"
                ><img src="assets/img/loader/color.gif" class="menu-spinner"
              /></span>
              <span
                class="badge badge-danger pull-right"
                *ngIf="
                  !structureService.signatureListServerSidePagination &&
                  !signatureCountLoading &&
                  !signatureLoadingError &&
                  (signatureRequest | signatureRequestUnreadCount: userData.userId)
                "
                >{{ signatureRequest | signatureRequestUnreadCount: userData.userId }}</span
              >
              <span
                class="badge badge-danger pull-right left-menu-unread-count"
                *ngIf="
                  structureService.signatureListServerSidePagination &&
                  !signatureCountLoading &&
                  !signatureLoadingError &&
                  structureService.signatureRequestUnreadCount > 0
                ">
                {{ structureService.signatureRequestUnreadCount }}
              </span>
            </a>
          </li>

          <!--                     <li [hidden]="(!previlages.superAdmin && !previlages.tenantConfig)" class="cat__menu-left__item" [ngClass]="((manageConfig.show_document_tagging != 1) || (crossTenantCommunicationEnabled && (crossTenantId && crossTenantId != tenantId)) ? ((manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1) ? 'cat__menu-left__item--disabled': 'cat__menu-left__item--hidden') : '')"
                        [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [routerLink]="['signature/signature-requests']">
                            <span class="cat__menu-left__icon icmn-pen"></span> Signature Requests
                            <span class="badge badge-danger pull-right" *ngIf="signatureCountLoading && !signatureLoadingError"><img src="assets/img/loader/color.gif" class="menu-spinner"></span>
                            <span class="badge badge-danger pull-right" *ngIf="!signatureCountLoading && !signatureLoadingError && (signatureRequest | signatureRequestUnreadCount)">{{(signatureRequest | signatureRequestUnreadCount)}}</span>
                        </a>
                    </li>
                    <li [hidden]="!previlages.superAdmin && !previlages.viewAllSignedDocs && !previlages.manageTenants" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [routerLink]="['signature/signed-documents']">
                            <span class="cat__menu-left__icon">SD</span> Signature Worklists
                        </a>
                    </li> -->
        </ul>
      </li>
      <li
        class="cat__menu-left__item cat__menu-left__submenu"
        [ngClass]="
          manageConfig.enable_forms != 1
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        ">
        <a class="inbox-collapse-count" href="javascript: void(0);" id="form_center">
          <!-- <span class="cat__menu-left__icon icmn-bubbles"></span> --><span class="cat__menu-left__icon"
            ><img style="width: 16px" src="./assets/img/forms-center.png"
          /></span>
          Form Center
          <span
            class="badge badge-danger pull-right"
            *ngIf="manageConfig.enable_forms == 1 && userInboxCounts && userInboxCounts.forms && userInboxCounts.forms.loading == true"
            ><img src="./assets/img/loader/color.gif" class="menu-spinner"
          /></span>
          <span
            class="badge badge-danger pull-right left-menu-unread-count"
            *ngIf="manageConfig.enable_forms == 1 && userInboxCounts && userInboxCounts.forms"
            [hidden]="
              userInboxCounts.forms &&
              (userInboxCounts.forms.loading == true ||
                userInboxCounts.forms.error == true ||
                (userInboxCounts.forms &&
                  userInboxCounts.forms.loading == false &&
                  userInboxCounts.forms.error == false &&
                  (!userInboxCounts.forms.totalCount || userInboxCounts.forms.totalCount < 1)))
            "
            >{{ userInboxCounts.forms.totalCount }}</span
          >
        </a>
        <ul class="cat__menu-left__list">
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']" *ngIf="previlages.FillStructuredForms">
            <a [routerLink]="['forms/send/list']" id="create_or_send_forms">
              <span class="cat__menu-left__icon"><i class="fa fa-list-alt" aria-hidden="true"></i></span>
              Create/Send Forms
            </a>
          </li>
          <li [hidden]="!previlages.allowScheduleForms" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['forms/scheduledforms']" id="schedule_forms">
              <span class="cat__menu-left__icon"><i aria-hidden="true" class="icon fa fa-calendar-check-o"></i></span> Schedule Forms
            </a>
          </li>
          <li
            class="cat__menu-left__item"
            [hidden]="!previlages.viewFormEntries || nursingAgencyUser"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a *ngIf="userData.accessSecurityEnabled" [routerLink]="['forms/worklist']" id="form_worklists_one">
              <span class="cat__menu-left__icon"><i class="fa fa-list-alt" aria-hidden="true"></i></span>
              Form Worklists
              <span
                class="badge badge-danger pull-right"
                *ngIf="manageConfig.enable_forms == 1 && userInboxCounts && userInboxCounts.forms && userInboxCounts.forms.loading == true"
                ><img src="./assets/img/loader/color.gif" class="menu-spinner"
              /></span>
              <span
                class="badge badge-danger pull-right left-menu-unread-count"
                *ngIf="manageConfig.enable_forms == 1 && userInboxCounts && userInboxCounts.forms"
                [hidden]="
                  userInboxCounts.forms &&
                  (userInboxCounts.forms.loading == true ||
                    userInboxCounts.forms.error == true ||
                    (userInboxCounts.forms &&
                      userInboxCounts.forms.loading == false &&
                      userInboxCounts.forms.error == false &&
                      (!userInboxCounts.forms.totalCount || userInboxCounts.forms.totalCount < 1)))
                "
                >{{ userInboxCounts.forms.totalCount }}</span
              >
            </a>
            <a *ngIf="!userData.accessSecurityEnabled" [routerLink]="['forms/list']" id="form_worklists_one">
              <span class="cat__menu-left__icon"><i class="fa fa-list-alt" aria-hidden="true"></i></span>
              Form Worklists
              <span
                class="badge badge-danger pull-right"
                *ngIf="manageConfig.enable_forms == 1 && userInboxCounts && userInboxCounts.forms && userInboxCounts.forms.loading == true"
                ><img src="./assets/img/loader/color.gif" class="menu-spinner"
              /></span>
              <span
                class="badge badge-danger pull-right left-menu-unread-count"
                *ngIf="manageConfig.enable_forms == 1 && userInboxCounts && userInboxCounts.forms"
                [hidden]="
                  userInboxCounts.forms &&
                  (userInboxCounts.forms.loading == true ||
                    userInboxCounts.forms.error == true ||
                    (userInboxCounts.forms &&
                      userInboxCounts.forms.loading == false &&
                      userInboxCounts.forms.error == false &&
                      (!userInboxCounts.forms.totalCount || userInboxCounts.forms.totalCount < 1)))
                "
                >{{ userInboxCounts.forms.totalCount }}</span
              >
            </a>
          </li>
          <li
            class="cat__menu-left__item"
            [hidden]="previlages.viewFormEntries && !nursingAgencyUser"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['forms/worklist']" id="form_worklists_two">
              <span class="cat__menu-left__icon"><i class="fa fa-list-alt" aria-hidden="true"></i></span>
              Form Worklists
              <span
                class="badge badge-danger pull-right"
                *ngIf="manageConfig.enable_forms == 1 && userInboxCounts && userInboxCounts.forms && userInboxCounts.forms.loading == true"
                ><img src="./assets/img/loader/color.gif" class="menu-spinner"
              /></span>
              <span
                class="badge badge-danger pull-right left-menu-unread-count"
                *ngIf="manageConfig.enable_forms == 1 && userInboxCounts && userInboxCounts.forms"
                [hidden]="
                  userInboxCounts.forms &&
                  (userInboxCounts.forms.loading == true ||
                    userInboxCounts.forms.error == true ||
                    (userInboxCounts.forms &&
                      userInboxCounts.forms.loading == false &&
                      userInboxCounts.forms.error == false &&
                      (!userInboxCounts.forms.totalCount || userInboxCounts.forms.totalCount < 1)))
                "
                >{{ userInboxCounts.forms.totalCount }}</span
              >
            </a>
          </li>
          <li class="cat__menu-left__item" *ngIf="manageConfig.enable_forms === '1' && manageConfig.enable_auto_form_send === '1' && previlages.manageAFS && !nursingAgencyUser"
          [routerLinkActive]="['cat__menu-left__item--active']">
          <a [routerLink]="['afs']" id="afs">
            <span class="cat__menu-left__icon"><i class="fa fa-cloud-upload" aria-hidden="true"></i></span> {{ 'MENU.AFS' | translate }}
          </a>
        </li>
        </ul>
      </li>
      <!--User Center Starts Here-->
      <li
        [hidden]="
          (!previlages.superAdmin && manageConfig.enable_user_center != 1) ||
          (!previlages.manageUserEnrollment && !previlages.allowAddUser) ||
          nursingAgencyUser
        "
        [ngClass]="
          manageConfig.enable_user_center != 1
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        "
        class="cat__menu-left__item cat__menu-left__submenu">
        <a class="inbox-collapse-count" href="javascript: void(0);" id="user_center">
          <span _ngcontent-c0="" class="cat__menu-left__icon fa fa-group"></span> User Center
        </a>
        <ul class="cat__menu-left__list">
          <li
            [hidden]="
              !previlages.allowAddUser ||
              (!previlages.addVirtualPatientUsers && !previlages.addVirtualEnrolledStaffUsers && !previlages.addVirtualPartnerUsers)
            "
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a *ngIf="previlages.allowAddUser && previlages.addVirtualPatientUsers" [routerLink]="['add-user/search-user']" id="add_user">
              <span _ngcontent-c0="" class="cat__menu-left__icon"><i _ngcontent-c0="" class="fa fa-user-plus"></i></span> Add Users
            </a>
            <a *ngIf="previlages.allowAddUser && !previlages.addVirtualPatientUsers" [routerLink]="['add-user/add']" id="add_user">
              <span _ngcontent-c0="" class="cat__menu-left__icon"><i _ngcontent-c0="" class="fa fa-user-plus"></i></span> Add Users
            </a>
          </li>
          <li
            [hidden]="!previlages.superAdmin && !previlages.manageUserEnrollment"
            class="cat__menu-left__item"
            [ngClass]="
              manageConfig.enable_user_enrollment != 1
                ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
                  ? 'cat__menu-left__item--disabled'
                  : 'cat__menu-left__item--hidden'
                : ''
            ">
            <a [routerLink]="['user-registrations']" id="invite_users">
              <span class="cat__menu-left__icon"><img style="width: 16px" src="./assets/img/share-icon.png" /></span> Invite Users
            </a>
          </li>
        </ul>
      </li>
      <!-- User Center ends-->

      <li
        [hidden]="!previlages.enableAppCenter"
        [ngClass]="{'cat__menu-left__item--hidden' : manageConfig.enable_app_center != 1,
        'cat__menu-left__item--disabled':manageConfig.menu_display_behaviour_of_disabled_feature 
        && manageConfig.menu_display_behaviour_of_disabled_feature == 1,
        'cat__menu-left__submenu':manageConfig.enable_app_center_left_menu 
        && manageConfig.enable_app_center_left_menu === '1',
        'cat__menu-left__submenu--toggled':toggleAppMenu}"
        class="cat__menu-left__item"
        [routerLinkActive]="['cat__menu-left__item--active']">
        <a [routerLink]="['Appcenter']" id="pulse_data" *ngIf="!(manageConfig.enable_app_center_left_menu 
        && manageConfig.enable_app_center_left_menu === '1'); else appCenterLeftMenu">
          <span class="cat__menu-left__icon"><i class="fa fa-cogs" aria-hidden="true"></i></span>
          {{'MENU.APP_CENTER' | translate}}
        </a>
        <ng-template #appCenterLeftMenu>
          <a href="javascript: void(0);" id="app-center-left-menu">
            <span class="cat__menu-left__icon"><i class="fa fa-cogs" aria-hidden="true"></i></span>
            {{'MENU.APP_CENTER' | translate}}
          </a>  
        </ng-template>
        <!-- App Center work-lists binding-->
        <ul  class="cat__menu-left__list" [ngClass]="previlages.enableAppCenter && manageConfig.enable_app_center_left_menu 
         && manageConfig.enable_app_center_left_menu === '1' ? '':'cat__menu-left__item--hidden'" *ngIf="appCenterData" > 
          <li class="cat__menu-left__item cat__menu-left__submenu"
            [ngClass]="{'cat__menu-left__submenu--toggled' : showAppCenterWorklistMenu === menuGroup.appName && toggleAppSubMenu}"
            [hidden]="menuGroup.worklists.length === 0"
            *ngFor="let menuGroup of appCenterData; let i = index" [attr.data-index]="i">
              <a href="javascript: void(0);" id="app-worklist-group">
                <span class="cat__menu-left__icon"><i class="fa fa-user-plus"></i></span> {{menuGroup.appName}}
              </a>
              <ul class="cat__menu-left__list"
                [ngClass]="{'show-worklist': showAppCenterWorklistMenu === menuGroup.appName && toggleAppSubMenu}">
                <li *ngFor="let menu of menuGroup.worklists; let i = index" class="cat__menu-left__item"
                  [routerLinkActive]="['cat__menu-left__item--active']">
                  <a [routerLink]="[menu.actionLink]" id="app-worklist-menu">
                    <span class="cat__menu-left__icon"><i class="{{menu.menuIcon}}"></i></span>
                      {{menu.menuTitle}}
                  </a>
                </li>
              </ul>
          </li>      
        </ul>
      </li>
      
      <!-- Education Training Center starts -->
      <li
        [ngClass]="
          manageConfig.show_education_training != 1
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        "
        class="cat__menu-left__item cat__menu-left__submenu">
        <a class="inbox-collapse-count" href="javascript: void(0);" id="education_center">
          <!-- <span class="cat__menu-left__icon icmn-bubbles"></span>--><span class="cat__menu-left__icon"
            ><img style="width: 16px" src="./assets/img/document-center.png"
          /></span>
          Education Center
        </a>
        <ul class="cat__menu-left__list">
          <li
            [hidden]="!previlages.enableEducationTraining || !canNaUserManageEdm"
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['education/education-material-list']" id="create_modify_education_material">
              <span class="cat__menu-left__icon icmn-pen"></span> Create/Modify Education Material
            </a>
          </li>
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['education/view-education-material-list']" id="view_education_materials">
              <span class="cat__menu-left__icon icmn-pen"></span> View Education Materials
            </a>
          </li>
        </ul>
      </li>
      <!-- Education Training Center ends-->

      <li
        class="cat__menu-left__item cat__menu-left__submenu"
        [ngClass]="{ 'cat__menu-left__submenu--toggled': showWorklistMenu === menuGroup.name && toggleMenu == true }"
        [hidden]="menuGroup.worklists.length == 0 || manageConfig.enable_worklist_center != 1 || nursingAgencyUser"
        *ngFor="let menuGroup of structureService.worklistMenu">
        <a href="javascript: void(0);" id="worklist-group">
          <span class="cat__menu-left__icon"><i class="fa fa-user-plus"></i></span> {{ menuGroup.name }}
        </a>
        <ul class="cat__menu-left__list" [ngClass]="{ 'show-worklist': showWorklistMenu === menuGroup.name && toggleMenu == true }">
          <li
            *ngFor="let menu of menuGroup.worklists; let i = index"
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="[menu.actionLink]" id="worklist-menu">
              <span class="cat__menu-left__icon"><i class="{{ menu.menuIcon }}"></i></span>
              {{ menu.menuTitle }}
            </a>
          </li>
        </ul>
      </li>
      <!-- <li *ngFor="let item of supplyMenuItems" [hidden]="!previlages.sentInventoryCount && !previlages.superAdmin && !previlages.manageTenants && item && !item.isdesktop" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']" [ngClass]="(manageConfig.show_staff_supply_inventory !=1 || (crossTenantCommunicationEnabled && (crossTenantId && crossTenantId != tenantId)) ? ((manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1) ? 'cat__menu-left__item--disabled' : 'cat__menu-left__item--hidden') : '')">
                <a [routerLink]="['forms/send/supply/list']">
                    <span class="cat__menu-left__icon">SS</span>{{item}}
                </a>
            </li> -->

      <!-- <li [hidden]="!previlages.superAdmin && !previlages.sendBroadcaseMessage" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                <a [routerLink]="['leads-dashboard']">
                    <span class="cat__menu-left__icon icmn-podcast"></span> Dashboard
                </a>
            </li>
            <li [hidden]="!previlages.superAdmin && !previlages.sendBroadcaseMessage" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                <a [routerLink]="['leads']">
                    <span class="cat__menu-left__icon icmn-podcast"></span> Leads
                </a>
            </li> -->
      <!-- <li class="cat__menu-left__item"
                *ngIf="manageConfig.enable_activity_hub ==1 && previlages.manageTenants && !nursingAgencyUser"
                [routerLinkActive]="['cat__menu-left__item--active']">
                <a [routerLink]="['activity-hub']">
                    <span class="cat__menu-left__icon  fa fa-group"></span> Patient Activities Hub
                </a>
            </li> -->
      <!-- <li *ngIf="accessSecurityWorklistSelected && accessSecurityWorklistActionLink && accessSecurityWorklistLabel && enableSecurityDashboard" class="cat__menu-left__item"
                [routerLinkActive]="['cat__menu-left__item--active']">
                <a [routerLink]="[accessSecurityWorklistActionLink]">
                    <span class="cat__menu-left__icon  fa fa-group"></span> {{accessSecurityWorklistLabel}}
                </a>
            </li> -->

      <li
        [hidden]="!previlages.enablePatientActivityHub || nursingAgencyUser || !_worklistService.showPahMenu"
        class="cat__menu-left__item"
        [routerLinkActive]="['cat__menu-left__item--active']">
        <a [routerLink]="[_worklistService.pahActionLink]" id="patient_activity_hub">
          <span class="cat__menu-left__icon fa fa-group"></span> Patient Activity Hub
        </a>
      </li>
      <li
        class="cat__menu-left__item" *ngIf="previlages.mangeCampaign && manageConfig.enable_campaign_manager === '1'"
        [routerLinkActive]="['cat__menu-left__item--active']">
        <a [routerLink]="['campaign-manager']" id="campaign-manager">
          <span class="cat__menu-left__icon"><i class="fa fa-cloud-upload" aria-hidden="true"></i></span> {{ 'MENU.CAMPAIGN_MANAGER' | translate }}&nbsp;<span class="menu-beta">BETA</span>
        </a>
      </li>
      <!-- View Nurser Scheduler -->
      <!-- <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']"
                [hidden]="(!previlages.formsLibrarymanager && !previlages.manageTenants)"
                [ngClass]="((manageConfig.enable_forms != 1)  ? ((manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1) ? 'cat__menu-left__item--disabled' : 'cat__menu-left__item--hidden') : '')">
                <a href="javascript: void(0);">
                    <span class="cat__menu-left__icon fa fa-calendar"></span> View Nurse Schedule
                </a>

            </li> -->

      <li *ngIf="manageConfig.enable_multisite === '1'"
        [ngClass]="
          manageConfig.enable_visit_schedule != 1
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        "
        class="cat__menu-left__item cat__menu-left__submenu">
        <a class="inbox-collapse-count" href="javascript: void(0);" id="schedule_center">
          <!-- <span class="cat__menu-left__icon icmn-bubbles"></span>--><span class="cat__menu-left__icon"
            ><img style="width: 16px" src="./assets/img/document-center.png"
          /></span>
          Schedule Center 
        </a>
        <ul class="cat__menu-left__list">
          <li
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="[routes.allVisits]" id="view_visit_schedule">
              <span class="cat__menu-left__icon fa fa-eye"></span> {{ 'TITLES.VIEW_VISITS' | translate }}
            </a>
          </li>
          <li *ngIf="previlages.allowRolesToSchedule || previlages.manageStaffAvailability"
            class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="[routes.staffUnavailability]" id="manage_manage_availability">
              <span class="cat__menu-left__icon icmn-pen"></span> {{ 'TITLES.MANAGE_AVAILABILITY' | translate }}
            </a>
          </li>
          <li *ngIf="visitSchedulerWorklistSelected && visitSchedulerWorklistActionLink && visitSchedulerWorklistRoleSelected == true"
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="[visitSchedulerWorklistActionLink]" id="visit_schedule_worklist">
              <span class="cat__menu-left__icon fa fa-group"></span> Schedule Center Worklist
            </a>
          </li>
        </ul>
      </li>
      <li *ngIf="manageConfig.enable_multisite !== '1'" [ngClass]="((manageConfig.enable_visit_schedule != 1) ? ((manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1) ? 'cat__menu-left__item--disabled' : 'cat__menu-left__item--hidden') : '' )"
      class="cat__menu-left__item cat__menu-left__submenu">
      <a class="inbox-collapse-count" href="javascript: void(0);" id="schedule_center">
          <!-- <span class="cat__menu-left__icon icmn-bubbles"></span>--><span
              class="cat__menu-left__icon"><img style="width:16px;"
                  src="./assets/img/document-center.png" /></span>  Schedule Center

      </a>
      <ul class="cat__menu-left__list">
          <li *ngIf="previlages.manageVisitSchedule || previlages.allowStaffToScheduleForThemselves"
              class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
              <a [routerLink]="['visit_scheduler/view-visits/scheduler']" id="create_modify_visit_schedule">
                  <span class="cat__menu-left__icon icmn-pen"></span> Create/Modify Visits
              </a>
          </li>
          <li *ngIf="userData.group!=3 && previlages.viewAllVisitSchedule " class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
              <a [routerLink]="['visit_scheduler/all-visits']" id="view_visit_schedule">
                  <span class="cat__menu-left__icon fa fa-eye"></span> View Visit Schedule
              </a>
          </li>
          <li *ngIf="userData.group==3 || !previlages.viewAllVisitSchedule" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
              <a [routerLink]="['visit_scheduler/my-visits']" id="view_visit_schedule">
                  <span class="cat__menu-left__icon fa fa-eye"></span> View Visit Schedule
              </a>
          </li>
          <li [hidden]="userData.group==3" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
              <a [routerLink]="['visit_scheduler/staff_unavailability/manage_staff_unavailability']" id="manage_manage_availability">
                  <span class="cat__menu-left__icon icmn-pen"></span> Manage Availability
              </a>
          </li>
          <li *ngIf="visitSchedulerWorklistSelected && visitSchedulerWorklistActionLink && visitSchedulerWorklistRoleSelected == true" class="cat__menu-left__item"
              [routerLinkActive]="['cat__menu-left__item--active']">
              <a [routerLink]="[visitSchedulerWorklistActionLink]" id="visit_schedule_worklist">
                  <span class="cat__menu-left__icon  fa fa-group"></span> Schedule Center Worklist
              </a>
          </li>
      </ul>
  </li>
      <!--

            <li  class="cat__menu-left__item"
                [ngClass]="((manageConfig.enable_nurse_schedule != 1) || (crossTenantCommunicationEnabled && (crossTenantId && crossTenantId != tenantId)) ? ((manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1) ? 'cat__menu-left__item--disabled' : 'cat__menu-left__item--hidden') : '')"
                [routerLinkActive]="['cat__menu-left__item--active']">
                <a [routerLink]="['booked-scheduler/schedule']">
                    <span class="cat__menu-left__icon fa fa-calendar"></span>  View Nurse Schedule
                </a>
            </li>

-->

      <!-- View Nurser Scheduler -->

      <li
        [hidden]="!previlages.clinicianApprover || nursingAgencyUser"
        class="cat__menu-left__item"
        [ngClass]="
          manageConfig.patient_referrals != 1 || (crossTenantCommunicationEnabled && crossTenantId && crossTenantId != tenantId)
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        "
        [routerLinkActive]="['cat__menu-left__item--active']">
        <a [routerLink]="['complete-referral-listing']" id="patient_referrals">
          <span class="cat__menu-left__icon fa fa-group"></span> Patient Referrals
        </a>
      </li>
      <li
        [hidden]="!previlages.allowPartnerPatientReferralInitiation || nursingAgencyUser"
        class="cat__menu-left__item"
        [ngClass]="
          manageConfig.patient_referrals != 1 || (crossTenantCommunicationEnabled && crossTenantId && crossTenantId != tenantId)
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        "
        [routerLinkActive]="['cat__menu-left__item--active']">
        <a [routerLink]="['patient-referral-listing']" id="initiate_patient_referral">
          <span class="cat__menu-left__icon fa fa-address-book-o"></span> Initiate Patient Referral
        </a>
      </li>
      <li
        [hidden]="(!previlages.superAdmin && !previlages.manageTenants) || nursingAgencyUser"
        class="cat__menu-left__item"
        [ngClass]="
          !enableFilingCenter
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        "
        [routerLinkActive]="['cat__menu-left__item--active']">
        <a [routerLink]="['file-manager']" id="filing_centres"> <span class="cat__menu-left__icon fa fa-server"></span> Filing Centers </a>
      </li>

      <li class="cat__menu-left__divider"></li>

      <li
        [hidden]="!previlages.showDashboard || nursingAgencyUser"
        class="cat__menu-left__item"
        [routerLinkActive]="['cat__menu-left__item--active']"
        [ngClass]="
          crossTenantCommunicationEnabled && crossTenantId && crossTenantId != tenantId
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        ">
        <a [routerLink]="['dashboard']" id="dashboard">
          <span class="cat__menu-left__icon"><i class="fa fa-tachometer" aria-hidden="true"></i></span>
          Dashboard
        </a>
      </li>
      <li
        [hidden]="!previlages.showFormReports || nursingAgencyUser"
        class="cat__menu-left__item"
        [routerLinkActive]="['cat__menu-left__item--active']"
        [ngClass]="
          crossTenantCommunicationEnabled && crossTenantId && crossTenantId != tenantId
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        ">
        <a [routerLink]="['form-reports']" id="form_reports">
          <span class="cat__menu-left__icon"><i class="fa fa-file-text-o" aria-hidden="true"></i></span>
          Form Reports
        </a>
      </li>
      <li
        [hidden]="(!previlages.superAdmin && !previlages.manageTenants) || nursingAgencyUser"
        class="cat__menu-left__item"
        [routerLinkActive]="['cat__menu-left__item--active']">
        <a
          [hidden]="!previlages.superAdmin && !previlages.manageTenants"
          routerLink="/account/account-settings/{{ accountId }}"
          id="account_settings">
          <span class="cat__menu-left__icon icmn-database"></span> Account Settings
        </a>
      </li>
      <li
        [hidden]="(!previlages.superAdmin && !previlages.manageTenants) || nursingAgencyUser"
        class="cat__menu-left__item"
        [routerLinkActive]="['cat__menu-left__item--active']"
        *ngIf="manageConfig.enable_multisite === '1'">
        <a [hidden]="!previlages.superAdmin && !previlages.manageTenants" [routerLink]="['manage-sites']" id="manage_sites">
          <span class="cat__menu-left__icon"><i class="fa fa-sitemap" aria-hidden="true"></i></span> Manage Sites
        </a>
      </li>
  
      <!-- <li [hidden]="(!previlages.superAdmin && !previlages.manageTenants)"
            class="cat__menu-left__item cat__menu-left__submenu"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a href="javascript: void(0);" id="master-data_settings">
                <span class="cat__menu-left__icon fa fa-th-large"></span> Master Data Settings
            </a>
            <ul class="cat__menu-left__list remove-ul">

                <li [hidden]="(!previlages.superAdmin && !previlages.manageTenants)" class="cat__menu-left__item"
                    [ngClass]="((manageConfig.show_form_features != 1) ? ((manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1) ? 'cat__menu-left__item--disabled' : 'cat__menu-left__item--hidden') : '')"
                    [routerLinkActive]="['cat__menu-left__item--active']">
                    <a [routerLink]="['master-data/manage']" id="master-data">
                        <span class="cat__menu-left__icon fa fa-list"></span>Manage Master Data Type
                    </a>
                </li>
            </ul>
        </li> -->
      <!--<li [hidden]="!previlages.superAdmin && !previlages.manageTenants" class="cat__menu-left__item cat__menu-left__submenu" [routerLinkActive]="['cat__menu-left__item--active']">
                <a href="javascript: void(0);">
                    <span class="cat__menu-left__icon icmn-cog"></span> Account Settings
                </a>

                 <ul class="cat__menu-left__list">
                    <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [hidden]="previlages.superAdmin" routerLink="/account/settings/{{accountId}}">
                            <span class="cat__menu-left__icon icmn-cog"></span> General Setup
                        </a>
                        <a [hidden]="!previlages.superAdmin" routerLink="/account/settings">
                            <span class="cat__menu-left__icon icmn-cog"></span> General Setup
                        </a>
                    </li>

                    <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [hidden]="previlages.superAdmin" routerLink="/account/account-settings/{{accountId}}">
                            <span class="cat__menu-left__icon icmn-cog"></span> Account Settings
                        </a>
                    </li> -->
      <!--<li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [routerLink]="['account/message-escalation']">
                            <span class="cat__menu-left__icon icmn-bin2"></span>
                            Message Escalation
                        </a>
                    </li>-->

      <!--<li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [routerLink]="['account/clinician-role']">
                            <span class="cat__menu-left__icon icmn-aid-kit"></span>
                            Clinician Roles
                        </a>
                    </li>-->
      <!--  <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [routerLink]="['account/preferences']">
                            <span class="cat__menu-left__icon icmn-equalizer2 "></span> Preferences & Features
                        </a>
                    </li>
                    <li [hidden]="!previlages.superAdmin" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [routerLink]="['related-institutions']">
                            <span class="cat__menu-left__icon icmn-office"></span> Related Accounts - P3
                        </a>
                    </li>
                </ul>

            </li> -->

      <!--<li *ngIf="previlages.verbalOrder" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                <a [routerLink]="['verbal-order']">
                    <span class="cat__menu-left__icon icmn-truck"></span>
                    New Verbal Order
                </a>
            </li>-->
      <li
        [hidden]="(!previlages.superAdmin && !previlages.infusionSupport) || nursingAgencyUser"
        class="cat__menu-left__item"
        [routerLinkActive]="['cat__menu-left__item--active']"
        [ngClass]="
          crossTenantCommunicationEnabled && crossTenantId && crossTenantId != tenantId
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        ">
        <a [routerLink]="['infusion-support']" id="infusion_support"> <span class="cat__menu-left__icon icmn-question"></span> Infusion Support </a>
      </li>
      <li [hidden]="!previlages.dashboard || nursingAgencyUser" class="cat__menu-left__divider"></li>
      <!-- <li [hidden]="(!previlages.superAdmin && !previlages.manageTenants) || nursingAgencyUser"
                class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']"
                [ngClass]="((crossTenantCommunicationEnabled && (crossTenantId && crossTenantId != tenantId) || (manageConfig.enable_message_center != 1)) ? ((manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1) ? 'cat__menu-left__item--disabled' : 'cat__menu-left__item--hidden') : '')">
                <a [routerLink]="['schedule']" id="message_routing_schedule">
                    <span class="cat__menu-left__icon icmn-calendar"></span> Message Routing Schedule
                </a>
            </li> -->

      <li
        [hidden]="
          manageConfig.enable_message_center != 1 ||
          (!previlages.superAdmin &&
            !previlages.manageChatLogs &&
            !previlages.manageTenants &&
            (!manageConfig.form_approver_clinician || manageConfig.form_approver_clinician.indexOf(userData.userId) == -1)) ||
          nursingAgencyUser
        "
        class="cat__menu-left__item cat__menu-left__submenu"
        [routerLinkActive]="['cat__menu-left__item--active']">
        <a href="javascript: void(0);" id="message_settings"> <span class="cat__menu-left__icon icmn-bubble2"></span> Message Settings </a>
        <ul class="cat__menu-left__list remove-ul">
          <li
            [hidden]="!previlages.superAdmin && !previlages.manageTenants"
            class="cat__menu-left__item"
            [ngClass]="
              manageConfig.show_form_features != 1
                ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
                  ? 'cat__menu-left__item--disabled'
                  : 'cat__menu-left__item--hidden'
                : ''
            "
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['message/tag-definitions']" id="message_tags"> <span class="cat__menu-left__icon fa fa-tags"></span>Message Tags </a>
          </li>
          <li
            [hidden]="!previlages.superAdmin && !previlages.manageTenants"
            class="cat__menu-left__item"
            [ngClass]="
              manageConfig.show_form_features != 1
                ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
                  ? 'cat__menu-left__item--disabled'
                  : 'cat__menu-left__item--hidden'
                : ''
            "
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['message/tag-type']" id="message_tag_types">
              <span class="cat__menu-left__icon"> <img style="width: 16px" src="./assets/img/tag-settings.png" /> </span>Message Tag Types
            </a>
          </li>
          <li
            [hidden]="!previlages.manageChatLogs"
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['message/logs']" id="chat_logs"> <span class="cat__menu-left__icon icmn-bubbles"></span> Chat Logs </a>
          </li>
          <li
            [hidden]="
              !previlages.superAdmin &&
              !previlages.manageTenants &&
              (!manageConfig.form_approver_clinician || manageConfig.form_approver_clinician.indexOf(userData.userId) == -1)
            "
            class="cat__menu-left__item"
            [ngClass]="
              manageConfig.show_form_features != 1
                ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
                  ? 'cat__menu-left__item--disabled'
                  : 'cat__menu-left__item--hidden'
                : ''
            "
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['documents/tagged-forms']" id="tagged_messages"> <span class="cat__menu-left__icon">TM</span> Tagged Messages </a>
          </li>
        </ul>
      </li>
      <li
        [hidden]="!previlages.superAdmin && !previlages.showManageAlert"
        [ngClass]="
          manageConfig.show_manage_alerts != 1
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        "
        class="cat__menu-left__item">
        <a class="inbox-collapse-count" [routerLink]="['/banner-alerts']" id="manage_alert_center">
          <span class="cat__menu-left__icon"><img style="width: 16px" src="./assets/img/alert.png" /></span> Banner Alerts
        </a>
      </li>
      <li
        [hidden]="!previlages.manageWorklistSettings || nursingAgencyUser"
        class="cat__menu-left__item cat__menu-left__submenu"
        [routerLinkActive]="['cat__menu-left__item--active']">
        <a href="javascript: void(0);" id="worklist_settings"> <span class="cat__menu-left__icon fa fa-list-alt"></span> Worklist Settings </a>
        <ul class="cat__menu-left__list">
          <!-- <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [routerLink]="['worklists/categories']" id="worklist_categories">
                            <span class="cat__menu-left__icon">WC</span> Worklist Categories
                        </a>
                    </li> -->
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['worklists/manage']" id="manage_worklist"> <span class="cat__menu-left__icon fa fa-th-list"></span> Manage Worklist </a>
          </li>
        </ul>
      </li>
      <li
        *ngIf="manageConfig.enable_refill_dashboard && manageConfig.enable_refill_dashboard === '1'"
        class="cat__menu-left__item cat__menu-left__submenu"
        [routerLinkActive]="['cat__menu-left__item--active']">
        <a href="javascript: void(0);" id="appcenter_settings"> <span class="cat__menu-left__icon fa fa-cogs"></span>{{ 'LABELS.APP_CENTER_SETTINGS' | translate }}</a>
        <ul class="cat__menu-left__list">
          <li *ngIf="manageConfig.enable_refill_dashboard && manageConfig.enable_refill_dashboard === '1'" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['app-center/configuration']" id="configuration"> <span class="cat__menu-left__icon fa fa-cog"></span>{{ 'CONFIGURATION.REFILL_DASHBOARD_CONFIGURATION' | translate }}</a>
          </li>
        </ul>
      </li>

      <li
        [hidden]="!previlages.dashboard || nursingAgencyUser"
        class="cat__menu-left__item"
        [routerLinkActive]="['cat__menu-left__item--active']"
        [ngClass]="
          crossTenantCommunicationEnabled && crossTenantId && crossTenantId != tenantId
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        ">
        <a [routerLink]="['dashboard']" id="dashboard_p2"> <span class="cat__menu-left__icon icmn-home"></span> Dashboard - P2 </a>
      </li>
      <li
        [hidden]="!previlages.superAdmin || nursingAgencyUser"
        class="cat__menu-left__item"
        [routerLinkActive]="['cat__menu-left__item--active']"
        [ngClass]="
          crossTenantCommunicationEnabled && crossTenantId && crossTenantId != tenantId
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        ">
        <a [routerLink]="['accounts']" id="accounts_sa_p2"> <span class="cat__menu-left__icon icmn-briefcase"></span> Accounts - SA P2 </a>
      </li>

      <li
        id="menu-left-vert-user-setting-container"
        [hidden]="
          (!previlages.clinicianApprover &&
            (!previlages.manageTenants || manageConfig.show_user_tagging != 1) &&
            !previlages.allowPatientEnrollment &&
            !previlages.allowStaffEnrollment &&
            !previlages.allowPartnerEnrollment) ||
          nursingAgencyUser
        "
        class="cat__menu-left__item cat__menu-left__submenu"
        [routerLinkActive]="['cat__menu-left__item--active']">
        <a id="menu-left-vert-user-setting-label" href="javascript: void(0);">
          <span class="cat__menu-left__icon icmn-users"></span> User Settings
        </a>
        <ul id="menu-left-vert-user-setting-ul" class="cat__menu-left__list">
          <li
            id="menu-left-vert-user-setting-staff"
            [hidden]="!previlages.clinicianApprover && !previlages.allowStaffEnrollment"
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['users/staff']" id="staff"> <span class="cat__menu-left__icon icmn-user-tie"></span> Staff </a>
          </li>
          <li
            id="menu-left-vert-user-setting-staff"
            [hidden]="!previlages.clinicianApprover && !previlages.allowPartnerEnrollment"
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['users/partners']" id="partners"> <span class="cat__menu-left__icon icmn-user-tie"></span> Partners </a>
          </li>
          <li
            [hidden]="!previlages.clinicianApprover && !previlages.patientApprover && !previlages.allowPatientEnrollment"
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['users/patients']" id="patients"> <span class="cat__menu-left__icon icmn-user"></span> Patients </a>
          </li>

          <li
            [hidden]="!previlages.manageTenants || manageConfig.show_user_tagging != 1"
            class="cat__menu-left__item"
            [ngClass]="
              manageConfig.show_user_tagging != 1
                ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
                  ? 'cat__menu-left__item--disabled'
                  : 'cat__menu-left__item--hidden'
                : ''
            "
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['users/user-tags']" id="user_tags"> <span class="cat__menu-left__icon fa fa-tags"></span> User Tags </a>
          </li>
          <li
            [hidden]="!previlages.manageTenants || manageConfig.show_user_tagging != 1"
            class="cat__menu-left__item"
            [ngClass]="
              manageConfig.show_user_tagging != 1
                ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
                  ? 'cat__menu-left__item--disabled'
                  : 'cat__menu-left__item--hidden'
                : ''
            "
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['users/tag-type']" id="user_tag_types">
              <span class="cat__menu-left__icon">
                <img style="width: 16px" src="./assets/img/tag-settings.png" />
              </span>
              User Tag Types
            </a>
          </li>

          <li
            [hidden]="!previlages.superAdmin && !previlages.subContractors"
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['users/patients']" id="subcontractors_p3">
              <span class="cat__menu-left__icon icmn-user-tie"></span> Subcontractors - P3
            </a>
          </li>

          <!--<li *ngIf="previlages.superAdmin || previlages.tenantConfig" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [routerLink]="['users/tag-definitions']">
                            <span class="cat__menu-left__icon">A</span> User Groups / Tags
                        </a>
                    </li>-->
        </ul>
      </li>

      <li
        class="cat__menu-left__item cat__menu-left__submenu"
        [routerLinkActive]="['cat__menu-left__item--active']"
        [hidden]="(!previlages.formsLibrarymanager && !previlages.manageTenants) || nursingAgencyUser"
        [ngClass]="
          manageConfig.enable_forms != 1
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        ">
        <a href="javascript: void(0);" id="form_settings"> <span class="cat__menu-left__icon icmn-file-text"></span> Form Settings </a>

        <ul class="cat__menu-left__list">
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']" [hidden]="!previlages.formsLibrarymanager">
            <a [routerLink]="['forms/manage']" id="manage_forms">
              <span class="cat__menu-left__icon"><i class="icmn-file-text" aria-hidden="true"></i></span>
              Manage Forms
            </a>
          </li>
          <li
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']"
            [hidden]="!previlages.formsLibrarymanager && !previlages.manageTenants">
            <a [routerLink]="['forms/form-tags']" id="form_types">
              <span class="cat__menu-left__icon"><i class="fa fa-list-alt" aria-hidden="true"></i></span>
              Form Types
            </a>
          </li>
          <li
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']"
            *ngIf="manageConfig.enable_custom_field_form_integration === '1'">
            <a [routerLink]="['form/custom-fields']" id="custom-field">
              <span class="cat__menu-left__icon"><i class="fa fa-list-alt" aria-hidden="true"></i></span>
              {{'LABELS.CUSTOM_FIELDS' | translate}}
            </a>
          </li>
          <li
            [hidden]="!previlages.manageMasterDataForms"
            class="cat__menu-left__item"
            [ngClass]="
              manageConfig.enable_masterdata_type != 1
                ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
                  ? 'cat__menu-left__item--disabled'
                  : 'cat__menu-left__item--hidden'
                : ''
            "
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['master-data/manage']" id="master-data">
              <span class="cat__menu-left__icon fa fa-list"></span>Manage Master Data Type
            </a>
          </li>
        </ul>
      </li>
      <li
        [hidden]="!previlages.ManageDocumentTemplates || nursingAgencyUser"
        class="cat__menu-left__item cat__menu-left__submenu"
        [ngClass]="
          +manageConfig.show_document_tagging !== 1 && !structureService.isDocumentManagementEnabled && !structureService.isDocumentCategoryEnabled
            ? menuDisplayClass
            : ''
        "
        [routerLinkActive]="['cat__menu-left__item--active']"
      >
        <a href="javascript: void(0);" id="document_settings">
          <span class="cat__menu-left__icon icmn-file-pdf"></span> {{ 'LABELS.DOCUMENT_SETTINGS' | translate }}
        </a>
        <ul class="cat__menu-left__list">
          <li
            *ngIf="previlages.ManageDocumentTemplates"
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']"
            [ngClass]="!structureService.isDocumentManagementEnabled ? menuDisplayClass : ''"
          >
            <a [routerLink]="['documents/manage-documents']" id="document_manage_document">
              <span class="cat__menu-left__icon">MD</span> {{ 'LABELS.MANAGE_DOCUMENTS' | translate }}
            </a>
          </li>

          <li
            *ngIf="previlages.ManageDocumentTemplates"
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']"
            [ngClass]="!structureService.isDocumentCategoryEnabled ? menuDisplayClass : ''"
          >
            <a [routerLink]="['documents/categories']" id="document_categories">
              <span class="cat__menu-left__icon">DC</span> {{ 'LABELS.DOCUMENT_CATEGORIES' | translate }}
            </a>
          </li>
          <li
            *ngIf="previlages.ManageDocumentTemplates"
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']"
            [ngClass]="+manageConfig.show_document_tagging !== 1 ? menuDisplayClass : ''"
          >
            <a [routerLink]="['documents/tag-definitions']" id="document_types">
              <span class="cat__menu-left__icon">DT</span> {{ 'LABELS.DOCUMENT_TYPES' | translate }}
            </a>
          </li>
        </ul>
      </li>
      <!--<li *ngIf="previlages.signatures" class="cat__menu-left__item cat__menu-left__submenu" [routerLinkActive]="['cat__menu-left__item--active']">
                <a href="javascript: void(0);">
                    <span class="cat__menu-left__icon icmn-pen"></span>
                    Signatures
                </a>
                <ul class="cat__menu-left__list">
                    <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [routerLink]="['signature/signed-documents']">
                            <span class="cat__menu-left__icon">SD</span>
                            Signed Documents
                        </a>
                    </li>
                    <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [routerLink]="['signature/signature-requests']">
                            <span class="cat__menu-left__icon">SR</span>
                            Signature Requests
                        </a>
                    </li>
                </ul>
            </li>-->

      <!--Visit Scheduling Settings Starts Here-->

      <!-- [hidden]="(!previlages.manageVisitScheduleConfigurations)"
                [ngClass]="((manageConfig.enable_visit_schedule != 1)  ? ((manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1) ? 'cat__menu-left__item--disabled' : 'cat__menu-left__item--hidden') : '')" -->

      <li
        *ngIf="manageConfig.enable_multisite === '1'"
        [hidden]="!previlages.manageVisitScheduleConfigurations"
        [ngClass]="
          manageConfig.enable_visit_schedule != 1
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        "
        class="cat__menu-left__item"
        [routerLinkActive]="['cat__menu-left__item--active']">
        <a [routerLink]="[routes.visitType]" id="visit_scheduling_settings">
          <span class="cat__menu-left__icon icmn-calendar"></span> Visit Scheduler Settings 
        </a>
      </li>
      <li *ngIf="manageConfig.enable_multisite !== '1'" [hidden]="(!previlages.manageVisitScheduleConfigurations)"
      [ngClass]="((manageConfig.enable_visit_schedule != 1)  ? ((manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1) ? 'cat__menu-left__item--disabled' : 'cat__menu-left__item--hidden') : '')"  
      class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
      <a  [routerLink]="['/visit_scheduler/manage_visit_types']" id="visit_scheduling_settings">
      <span class="cat__menu-left__icon icmn-calendar"></span> Visit Scheduler Settings 
      </a>
      </li>
      <li
        [hidden]="!previlages.superAdmin || nursingAgencyUser"
        class="cat__menu-left__item cat__menu-left__submenu"
        [routerLinkActive]="['cat__menu-left__item--active']"
        [ngClass]="
          crossTenantCommunicationEnabled && crossTenantId && crossTenantId != tenantId
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        ">
        <a href="javascript: void(0);" id="faqs_p2"> <span class="cat__menu-left__icon icmn-question"></span> FAQs - P2 </a>
        <ul class="cat__menu-left__list">
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['pages/page-404']" id="connected_to_wordPress">
              <span class="cat__menu-left__icon" id="sd">SD</span> Connected to WordPress
            </a>
          </li>
        </ul>
      </li>
      <!-- <li [hidden]="!previlages.sentInventoryCount && (!previlages.superAdmin && !previlages.manageTenants && !previlages.viewAllInventoryReport)" class="cat__menu-left__item cat__menu-left__submenu" [ngClass]="((((manageConfig.show_patient_supply_inventory !=1 && userData.group == 3) || (manageConfig.show_staff_supply_inventory !=1 && userData.group != 3) || (crossTenantCommunicationEnabled && (crossTenantId && crossTenantId != tenantId)))) ? ((manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1) ? 'cat__menu-left__item--disabled' : 'cat__menu-left__item--hidden') : '')"
                [routerLinkActive]="['cat__menu-left__item--active']">
                <a href="javascript: void(0);">
                    <span class="cat__menu-left__icon icmn-truck"></span> Supplies
                </a>
                <ul class="cat__menu-left__list">
                    <li [hidden]="!previlages.viewAllInventoryReport && !previlages.superAdmin && !previlages.manageTenants" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [routerLink]="['supplies/inventory-submissions']">
                            <span class="cat__menu-left__icon">SS</span> Supply Worklists
                        </a>
                    </li>
                </ul>
            </li> -->
      <li
        [hidden]="(!previlages.superAdmin && !previlages.manageTenants) || nursingAgencyUser"
        class="cat__menu-left__item cat__menu-left__submenu"
        [routerLinkActive]="['cat__menu-left__item--active']">
        <a href="javascript: void(0);" id="roles_and_privilages">
          <span class="cat__menu-left__icon"><i class="fa fa-users" aria-hidden="true"></i></span> Roles & Privileges
        </a>

        <ul class="cat__menu-left__list">
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['roles-and-privileges/staff/role']" id="staff"> <span class="cat__menu-left__icon" id="s">S</span> Staff </a>
          </li>
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['roles-and-privileges/partner/role']" id="partner"> <span class="cat__menu-left__icon" id="p">P</span> Partner </a>
          </li>
          <li
            [hidden]="!previlages.superAdmin && !previlages.subContractors"
            class="cat__menu-left__item"
            [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['pages/page-404']" id="subcontractors_p3"> <span class="cat__menu-left__icon" id="s">S</span> Subcontractors - P3 </a>
          </li>
        </ul>
      </li>
      <!-- <li *ngIf="previlages.supplyRequests" class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
                <a [routerLink]="['supplies/supply-request']">
                    <span class="cat__menu-left__icon icmn-truck"></span>
                    Supply Counts
                </a>
            </li> -->
      <li
        [hidden]="!previlages.superAdmin || nursingAgencyUser"
        class="cat__menu-left__item cat__menu-left__submenu"
        [routerLinkActive]="['cat__menu-left__item--active']"
        [ngClass]="
          crossTenantCommunicationEnabled && crossTenantId && crossTenantId != tenantId
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        ">
        <a href="javascript: void(0);" id="enrollment_P2"> <span class="cat__menu-left__icon icmn-newspaper"></span> Enrollment - P2 </a>
        <ul class="cat__menu-left__list">
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['pages/page-404']" id="message_templates"> <span class="cat__menu-left__icon icmn-mail3"></span> Message Templates </a>
          </li>
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['pages/page-404']" id="referral_activities">
              <span class="cat__menu-left__icon icmn-books"></span> Referral Activities
            </a>
          </li>
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['pages/page-404']" id="referral_sources"> <span class="cat__menu-left__icon icmn-drive"></span> Referral Sources </a>
          </li>
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['pages/page-404']" id="enrollment_tokens"> <span class="cat__menu-left__icon icmn-key2"></span> Enrollment Tokens </a>
          </li>
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['pages/page-404']" id="configuration_settings">
              <span class="cat__menu-left__icon icmn-cog"></span> Configuration Settings
            </a>
          </li>
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['pages/page-404']" id="referral_registration_tokens">
              <span class="cat__menu-left__icon icmn-key"></span> Referral Registration Tokens
            </a>
          </li>
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['pages/page-404']" id="referral_token_types"> <span class="cat__menu-left__icon">RR</span> Referral Token Types </a>
          </li>
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['pages/page-404']" id="referral_user_types"> <span class="cat__menu-left__icon">RU</span> Referral User Types </a>
          </li>
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['pages/page-404']" id="referral_source_types"> <span class="cat__menu-left__icon">RS</span> Referral Source Types </a>
          </li>
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['pages/page-404']" id="referral_institutions"> <span class="cat__menu-left__icon">RI</span> Referral Institutions </a>
          </li>
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['pages/page-404']" id="regitration_requests"> <span class="cat__menu-left__icon">RR</span> Registration Requests </a>
          </li>
        </ul>
      </li>
      <li [hidden]="!previlages.superAdmin || nursingAgencyUser" class="cat__menu-left__divider"></li>
      <li
        [hidden]="!previlages.superAdmin || nursingAgencyUser"
        class="cat__menu-left__item cat__menu-left__submenu"
        [routerLinkActive]="['cat__menu-left__item--active']"
        [ngClass]="
          crossTenantCommunicationEnabled && crossTenantId && crossTenantId != tenantId
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        ">
        <a href="javascript: void(0);" id="developer_tools"> <span class="cat__menu-left__icon icmn-cogs"></span> Developer Tools </a>
        <ul class="cat__menu-left__list">
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['developer-tools/graphiql']" id="graphiQL"> <span class="cat__menu-left__icon icmn-mail3"></span> GraphiQL </a>
          </li>
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['developer-tools/graphql-rover']" id="graphiQL_rover">
              <span class="cat__menu-left__icon icmn-drive"></span> GraphQL Rover
            </a>
          </li>
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['developer-tools/powerbi-reports']" id="powerBI_reports">
              <span class="cat__menu-left__icon icmn-books"></span> PowerBI Reports
            </a>
          </li>
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['developer-tools/watchtower-telemetry']" id="watchtower_telemetry">
              <span class="cat__menu-left__icon icmn-key2"></span> Watchtower Telemetry
            </a>
          </li>
        </ul>
      </li>

      <li
        [hidden]="!previlages.superAdmin || nursingAgencyUser"
        class="cat__menu-left__item cat__menu-left__submenu"
        [routerLinkActive]="['cat__menu-left__item--active']"
        [ngClass]="
          crossTenantCommunicationEnabled && crossTenantId && crossTenantId != tenantId
            ? manageConfig.menu_display_behaviour_of_disabled_feature && manageConfig.menu_display_behaviour_of_disabled_feature == 1
              ? 'cat__menu-left__item--disabled'
              : 'cat__menu-left__item--hidden'
            : ''
        ">
        <a href="javascript: void(0);" id="ops_tools"> <span class="cat__menu-left__icon icmn-cog"></span> Ops Tools </a>
        <ul class="cat__menu-left__list">
          <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']">
            <a [routerLink]="['ops-tools/os-query']" id="os_query"> <span class="cat__menu-left__icon icmn-mail3"></span> osQuery </a>
          </li>
        </ul>
      </li>
      <li
        [hidden]="(!previlages.superAdmin && !previlages.manageTenants) || nursingAgencyUser"
        class="cat__menu-left__item"
        [routerLinkActive]="['cat__menu-left__item--active']">
        <a [hidden]="!previlages.superAdmin && !previlages.manageTenants" [routerLink]="['activity-logs']" id="activity_logs">
          <span class="cat__menu-left__icon"><i class="fa fa-history" aria-hidden="true"></i></span> {{ 'MENU.ACTIVITY_LOGS' | translate }}&nbsp;<span class="menu-beta">BETA</span>
        </a>
      </li>
      <div class="app-version" id="appVersion">Version: {{ appVersion }}</div>
    </ul>
  </div>
</nav>

<div id="video-call-notify" class="video_call_notify">
  <div class="video_notify_cntnt" id="video-notify-cntnt"> </div>
  <div class="video_notify_btns">
    <button (click)="acceptCall()" class="btn btn-success" id="accept">{{'BUTTONS.ACCEPT' | translate}}</button>
    <button (click)="rejectCall()" class="btn btn-danger" id="reject">{{'BUTTONS.REJECT' | translate}}</button>
  </div>
</div>
