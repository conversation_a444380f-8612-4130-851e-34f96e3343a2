<section class="card">
  <div class="card-header">
    <span class="cat__core__title">
      <strong>Form Reports</strong>
    </span>
  </div>
  
  <div class="card-block">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
      <li class="breadcrumb-item active">Form Reports</li>
    </ol>
    
    <form [formGroup]="formReportsForm" (ngSubmit)="onSubmit()" class="form-horizontal form-reports-form">
      <div class="form-body">
        
        <!-- Single Row Layout -->
        <div class="row form-reports-row">
          
          <!-- Form Status -->
          <div class="col-md-2 col-sm-6 col-12 mb-3">
            <label class="form-label">Form Status *</label>
            <angular2-multiselect
              [data]="formStatusOptions"
              [settings]="formStatusSettings"
              (onSelect)="onFormStatusSelect($event)"
              (onDeSelect)="onFormStatusDeselect()"
              formControlName="formStatus">
            </angular2-multiselect>
            <div class="text-danger" *ngIf="formReportsForm.get('formStatus')?.touched && formReportsForm.get('formStatus')?.errors?.['required']">
              Form Status is required
            </div>
          </div>
          
          <!-- Specific Form -->
          <div class="col-md-2 col-sm-6 col-12 mb-3">
            <label class="form-label">Specific Form</label>
            <angular2-multiselect
              [data]="filteredFormOptions"
              [settings]="specificFormSettings"
              (onSelect)="onSpecificFormSelect($event)"
              (onDeSelect)="onSpecificFormDeselect()"
              (onFilterChange)="onSpecificFormFilterChange($event)"
              formControlName="specificForm">
            </angular2-multiselect>
          </div>
          
          <!-- Site -->
          <div class="col-md-2 col-sm-6 col-12 mb-3">
            <label class="form-label">Site</label>
            <app-select-sites 
              [events]="eventsSubject.asObservable()" 
              [hideApplyFilter]="true" 
              [singleSelection]="true" 
              [siteSelection]="true" 
              [filterType]="true" 
              [selectedSiteIds]="selectedSiteIds"
              (siteIds)="getSiteIds($event)" 
              (hideDropdown)="hideDropdown($event)">
            </app-select-sites>
          </div>
          
          <!-- MRN -->
          <div class="col-md-1 col-sm-6 col-12 mb-3">
            <label class="form-label">MRN</label>
            <input 
              type="text" 
              class="form-control" 
              formControlName="mrn"
              placeholder="Enter MRN">
          </div>
          
          <!-- First Name Starts With -->
          <div class="col-md-1 col-sm-6 col-12 mb-3">
            <label class="form-label">First Name Starts With</label>
            <input 
              type="text" 
              class="form-control" 
              formControlName="firstNameStartsWith"
              placeholder="First name">
          </div>
          
          <!-- Last Name Starts With -->
          <div class="col-md-1 col-sm-6 col-12 mb-3">
            <label class="form-label">Last Name Starts With</label>
            <input 
              type="text" 
              class="form-control" 
              formControlName="lastNameStartsWith"
              placeholder="Last name">
          </div>
          
          <!-- Clinician (Contains) -->
          <div class="col-md-1 col-sm-6 col-12 mb-3">
            <label class="form-label">Clinician (Contains)</label>
            <input 
              type="text" 
              class="form-control" 
              formControlName="clinicianContains"
              placeholder="Clinician">
          </div>
          
          <!-- Drug Name (Contains) -->
          <div class="col-md-1 col-sm-6 col-12 mb-3">
            <label class="form-label">Drug Name (Contains)</label>
            <input 
              type="text" 
              class="form-control" 
              formControlName="drugNameContains"
              placeholder="Drug name">
          </div>
          
          <!-- Missing Records Toggle -->
          <div class="col-md-1 col-sm-6 col-12 mb-3 d-flex flex-column">
            <label class="form-label">Missing Records</label>
            <div class="form-check form-switch mt-2">
              <input 
                class="form-check-input" 
                type="checkbox" 
                formControlName="missingRecords"
                id="missingRecordsToggle">
              <label class="form-check-label" for="missingRecordsToggle">
                {{ formReportsForm.get('missingRecords')?.value ? 'On' : 'Off' }}
              </label>
            </div>
          </div>
          
        </div>
        
        <!-- Action Buttons -->
        <div class="row mt-4">
          <div class="col-12">
            <div class="form-actions d-flex gap-3">
              <button 
                type="submit" 
                class="btn btn-primary"
                [disabled]="isSubmitting || !formReportsForm.valid">
                <i class="fa fa-spinner fa-spin" *ngIf="isSubmitting"></i>
                {{ isSubmitting ? 'Generating...' : 'Submit' }}
              </button>
              
              <button 
                type="button" 
                class="btn btn-success"
                *ngIf="showDownloadButton"
                (click)="downloadReport()">
                <i class="fa fa-download"></i>
                Download Report
              </button>
              
              <button 
                type="button" 
                class="btn btn-secondary"
                (click)="resetForm()">
                Reset
              </button>
            </div>
          </div>
        </div>
        
      </div>
    </form>
  </div>
</section>
