.form-reports-form {
  .form-reports-row {
    display: flex;
    flex-wrap: wrap;
    align-items: end;
    gap: 15px;
    
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      gap: 10px;
    }
  }
  
  .form-label {
    font-weight: 500;
    margin-bottom: 5px;
    color: #333;
    font-size: 0.9rem;
    
    @media (max-width: 768px) {
      font-size: 0.85rem;
    }
  }
  
  .form-control {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
    
    &:focus {
      border-color: #007bff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    @media (max-width: 768px) {
      font-size: 0.85rem;
      padding: 10px 12px;
    }
  }
  
  .form-check-input {
    transform: scale(1.2);
    margin-right: 8px;
  }
  
  .form-check-label {
    font-size: 0.9rem;
    color: #666;
  }
  
  .form-actions {
    padding-top: 20px;
    border-top: 1px solid #eee;
    
    .btn {
      min-width: 120px;
      font-weight: 500;
      
      @media (max-width: 768px) {
        min-width: 100px;
        font-size: 0.9rem;
      }
    }
    
    .btn-primary {
      background-color: #007bff;
      border-color: #007bff;
      
      &:hover:not(:disabled) {
        background-color: #0056b3;
        border-color: #0056b3;
      }
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
    
    .btn-success {
      background-color: #28a745;
      border-color: #28a745;
      
      &:hover {
        background-color: #218838;
        border-color: #218838;
      }
    }
    
    .btn-secondary {
      background-color: #6c757d;
      border-color: #6c757d;
      
      &:hover {
        background-color: #545b62;
        border-color: #545b62;
      }
    }
  }
  
  .text-danger {
    font-size: 0.8rem;
    margin-top: 4px;
  }
}

// Angular multiselect dropdown customization
:host ::ng-deep {
  .form-reports-dropdown {
    .dropdown-btn {
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 8px 12px;
      font-size: 0.9rem;
      min-height: 38px;
      
      &:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }
    }
    
    .dropdown-list {
      border: 1px solid #ddd;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .list-area {
        max-height: 200px;
        
        li {
          padding: 8px 12px;
          font-size: 0.9rem;
          
          &:hover {
            background-color: #f8f9fa;
          }
          
          &.selected {
            background-color: #007bff;
            color: white;
          }
        }
      }
    }
  }
}

// Site selector component styling
:host ::ng-deep {
  app-select-sites {
    .select-sites {
      .dropdown-btn {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 8px 12px;
        font-size: 0.9rem;
        min-height: 38px;
      }
    }
  }
}

// Card styling to match existing theme
.card {
  border: 1px solid #e3e6f0;
  border-radius: 0.35rem;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);

  .card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.25rem;

    .cat__core__title {
      color: #5a5c69;
      font-size: 1.25rem;
      font-weight: 400;
    }
  }

  .card-block {
    padding: 1.25rem;
  }
}

// Breadcrumb styling
.breadcrumb {
  background-color: transparent;
  padding: 0.75rem 0;
  margin-bottom: 1.5rem;

  .breadcrumb-item {
    color: #858796;

    a {
      color: #5a5c69;
      text-decoration: none;

      &:hover {
        color: #3a3b45;
        text-decoration: underline;
      }
    }

    &.active {
      color: #6c757d;
    }
  }
}

// Responsive adjustments
@media (max-width: 1200px) {
  .form-reports-form .form-reports-row {
    .col-md-2 {
      flex: 0 0 calc(25% - 15px);
      max-width: calc(25% - 15px);
    }

    .col-md-1 {
      flex: 0 0 calc(20% - 15px);
      max-width: calc(20% - 15px);
    }
  }
}

@media (max-width: 992px) {
  .form-reports-form .form-reports-row {
    .col-md-2,
    .col-md-1 {
      flex: 0 0 calc(33.333% - 15px);
      max-width: calc(33.333% - 15px);
    }
  }
}

@media (max-width: 768px) {
  .form-reports-form .form-reports-row {
    .col-md-2,
    .col-md-1 {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }

  .form-actions {
    .d-flex {
      flex-direction: column;
      gap: 10px !important;

      .btn {
        width: 100%;
      }
    }
  }

  .card-block {
    padding: 1rem;
  }
}

@media (max-width: 576px) {
  .form-reports-form {
    .form-label {
      font-size: 0.8rem;
    }

    .form-control {
      font-size: 0.8rem;
      padding: 8px 10px;
    }

    .btn {
      font-size: 0.8rem;
      padding: 0.5rem 1rem;
    }
  }

  .card-header {
    padding: 0.75rem 1rem;

    .cat__core__title {
      font-size: 1.1rem;
    }
  }
}
