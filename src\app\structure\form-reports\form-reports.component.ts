import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { HttpService } from '../../services/http/http.service';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';

@Component({
  selector: 'app-form-reports',
  templateUrl: './form-reports.component.html',
  styleUrls: ['./form-reports.component.scss']
})
export class FormReportsComponent implements OnInit, OnDestroy {
  formReportsForm: FormGroup;
  userData: any;
  isSubmitting = false;
  showDownloadButton = false;
  downloadUrl: string = '';
  
  // Form Status options
  formStatusOptions = [
    { id: 'active', itemName: 'Active' },
    { id: 'inactive', itemName: 'Inactive' }
  ];
  
  // Specific Form options (will be populated via API)
  specificFormOptions: any[] = [];
  filteredFormOptions: any[] = [];
  
  // Site selector
  eventsSubject: Subject<any> = new Subject<any>();
  selectedSiteIds: any;
  hideSiteSelection = true;
  
  // Dropdown settings
  formStatusSettings = {
    singleSelection: true,
    text: 'Select Form Status',
    classes: 'form-reports-dropdown',
    enableSearchFilter: false
  };
  
  specificFormSettings = {
    singleSelection: true,
    text: 'Select Specific Form',
    classes: 'form-reports-dropdown',
    enableSearchFilter: true
  };

  constructor(
    private formBuilder: FormBuilder,
    private httpService: HttpService,
    private structureService: StructureService,
    private toolTipService: ToolTipService
  ) {
    this.userData = JSON.parse(this.structureService.userDetails || '{}');
  }

  ngOnInit(): void {
    this.initializeForm();
    this.loadSpecificForms();
  }

  ngOnDestroy(): void {
    this.eventsSubject.complete();
  }

  initializeForm(): void {
    this.formReportsForm = this.formBuilder.group({
      formStatus: ['', Validators.required],
      specificForm: [''],
      site: [''],
      mrn: [''],
      firstNameStartsWith: [''],
      lastNameStartsWith: [''],
      clinicianContains: [''],
      drugNameContains: [''],
      missingRecords: [false]
    });
  }

  loadSpecificForms(): void {
    // TODO: Replace with actual API endpoint
    const apiEndpoint = '/api/forms/list';
    
    this.httpService.doGet(apiEndpoint).subscribe(
      (response: any) => {
        if (response && response.data) {
          this.specificFormOptions = response.data.map((form: any) => ({
            id: form.id,
            itemName: form.name
          }));
          this.filteredFormOptions = [...this.specificFormOptions];
        }
      },
      (error) => {
        console.error('Error loading forms:', error);
        // Fallback data for development
        this.specificFormOptions = [
          { id: '1', itemName: 'Patient Intake Form' },
          { id: '2', itemName: 'Medication Review Form' },
          { id: '3', itemName: 'Discharge Summary Form' }
        ];
        this.filteredFormOptions = [...this.specificFormOptions];
      }
    );
  }

  onFormStatusSelect(item: any): void {
    this.formReportsForm.patchValue({ formStatus: item.id });
  }

  onFormStatusDeselect(): void {
    this.formReportsForm.patchValue({ formStatus: '' });
  }

  onSpecificFormSelect(item: any): void {
    this.formReportsForm.patchValue({ specificForm: item.id });
  }

  onSpecificFormDeselect(): void {
    this.formReportsForm.patchValue({ specificForm: '' });
  }

  onSpecificFormFilterChange(searchText: string): void {
    if (!searchText) {
      this.filteredFormOptions = [...this.specificFormOptions];
    } else {
      // Filter and sort by relevance - exact matches first, then starts with, then contains
      const searchLower = searchText.toLowerCase();
      this.filteredFormOptions = this.specificFormOptions
        .filter(form => form.itemName.toLowerCase().includes(searchLower))
        .sort((a, b) => {
          const aName = a.itemName.toLowerCase();
          const bName = b.itemName.toLowerCase();

          // Exact match first
          if (aName === searchLower) return -1;
          if (bName === searchLower) return 1;

          // Starts with second
          if (aName.startsWith(searchLower) && !bName.startsWith(searchLower)) return -1;
          if (bName.startsWith(searchLower) && !aName.startsWith(searchLower)) return 1;

          // Alphabetical order for the rest
          return aName.localeCompare(bName);
        });
    }
  }

  getSiteIds(data: any): void {
    this.selectedSiteIds = data.siteId;
    this.formReportsForm.patchValue({ site: data.siteId });
  }

  hideDropdown(hideItem: any): void {
    this.hideSiteSelection = hideItem.hideItem;
  }

  onSubmit(): void {
    if (this.formReportsForm.valid) {
      this.isSubmitting = true;
      this.showDownloadButton = false;
      
      const formData = {
        ...this.formReportsForm.value,
        siteId: this.selectedSiteIds
      };
      
      // TODO: Replace with actual API endpoint
      const submitEndpoint = '/api/form-reports/generate';
      
      this.httpService.doPost(submitEndpoint, formData).subscribe(
        (response: any) => {
          this.isSubmitting = false;
          if (response) {
            // Handle different response types
            if (response.downloadUrl) {
              this.downloadUrl = response.downloadUrl;
              this.showDownloadButton = true;
            } else if (response.fileData) {
              // Handle direct file data response
              this.downloadFileFromData(response.fileData, response.fileName || 'form-report.xlsx');
            } else if (response.success) {
              // Handle success response without immediate download
              this.showDownloadButton = false;
            }

            this.structureService.notifyMessage({
              message: response.message || 'Report generated successfully!',
              delay: 3000
            });
          }
        },
        (error) => {
          this.isSubmitting = false;
          console.error('Error generating report:', error);

          let errorMessage = 'Error generating report. Please try again.';
          if (error.error && error.error.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }

          this.structureService.notifyMessage({
            message: errorMessage,
            delay: 5000
          });
        }
      );
    } else {
      this.markFormGroupTouched();
    }
  }

  downloadReport(): void {
    if (this.downloadUrl) {
      try {
        // Create a temporary link element and trigger download
        const link = document.createElement('a');
        link.href = this.downloadUrl;

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `form-report-${timestamp}.xlsx`;
        link.download = filename;

        // Append to body, click, and remove
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.structureService.notifyMessage({
          message: 'Report downloaded successfully!',
          delay: 3000
        });
      } catch (error) {
        console.error('Error downloading report:', error);
        this.structureService.notifyMessage({
          message: 'Error downloading report. Please try again.',
          delay: 3000
        });
      }
    }
  }

  resetForm(): void {
    this.formReportsForm.reset();
    this.formReportsForm.patchValue({ missingRecords: false });
    this.showDownloadButton = false;
    this.downloadUrl = '';
    this.eventsSubject.next('reset');
  }

  private downloadFileFromData(fileData: string, fileName: string): void {
    try {
      // Convert base64 to blob
      const byteCharacters = atob(fileData);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      this.structureService.notifyMessage({
        message: 'Report downloaded successfully!',
        delay: 3000
      });
    } catch (error) {
      console.error('Error downloading file from data:', error);
      this.structureService.notifyMessage({
        message: 'Error downloading report. Please try again.',
        delay: 3000
      });
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.formReportsForm.controls).forEach(key => {
      const control = this.formReportsForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }
}
