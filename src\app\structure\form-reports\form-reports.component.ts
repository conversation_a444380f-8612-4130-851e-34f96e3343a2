import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';

@Component({
  selector: 'app-form-reports',
  templateUrl: './form-reports.component.html',
  styleUrls: ['./form-reports.component.scss']
})
export class FormReportsComponent implements OnInit, OnDestroy {
  formReportsForm: FormGroup;
  userData: any;
  isSubmitting = false;
  showDownloadButton = false;
  downloadUrl: string = '';
  
  // Form Status options
  formStatusOptions = [
    { id: 'active', itemName: 'Active' },
    { id: 'inactive', itemName: 'Inactive' }
  ];
  
  // Specific Form options (will be populated via API)
  specificFormOptions: any[] = [];
  filteredFormOptions: any[] = [];
  
  // Site selector
  eventsSubject: Subject<any> = new Subject<any>();
  selectedSiteIds: any;
  hideSiteSelection = true;
  
  // Dropdown settings
  formStatusSettings = {
    singleSelection: true,
    text: 'Select Form Status',
    classes: 'form-reports-dropdown',
    enableSearchFilter: false
  };
  
  specificFormSettings = {
    singleSelection: true,
    text: 'Select Specific Form',
    classes: 'form-reports-dropdown',
    enableSearchFilter: true
  };

  constructor(
    private formBuilder: FormBuilder,
    private structureService: StructureService,
    private toolTipService: ToolTipService
  ) {
    this.userData = JSON.parse(this.structureService.userDetails || '{}');
  }

  ngOnInit(): void {
    this.initializeForm();
    this.loadSpecificForms();
  }

  ngOnDestroy(): void {
    this.eventsSubject.complete();
  }

  initializeForm(): void {
    this.formReportsForm = this.formBuilder.group({
      formStatus: ['', Validators.required],
      specificForm: [''],
      site: [''],
      mrn: [''],
      firstNameStartsWith: [''],
      lastNameStartsWith: [''],
      clinicianContains: [''],
      drugNameContains: [''],
      missingRecords: [false]
    });
  }

  loadSpecificForms(): void {
    // Static data for demonstration - replace with actual API call when backend is ready
    this.specificFormOptions = [
      { id: '1', itemName: 'Patient Registration Form' },
      { id: '2', itemName: 'Medical History Form' },
      { id: '3', itemName: 'Consent Form' },
      { id: '4', itemName: 'Insurance Information Form' },
      { id: '5', itemName: 'Emergency Contact Form' },
      { id: '6', itemName: 'Medication List Form' },
      { id: '7', itemName: 'Allergy Information Form' },
      { id: '8', itemName: 'Pre-Visit Questionnaire' },
      { id: '9', itemName: 'Post-Visit Survey' },
      { id: '10', itemName: 'Appointment Scheduling Form' },
      { id: '11', itemName: 'Lab Results Form' },
      { id: '12', itemName: 'Prescription Request Form' },
      { id: '13', itemName: 'Referral Form' },
      { id: '14', itemName: 'Treatment Plan Form' },
      { id: '15', itemName: 'Follow-up Care Form' }
    ];
    this.filteredFormOptions = [...this.specificFormOptions];
  }

  onFormStatusSelect(item: any): void {
    this.formReportsForm.patchValue({ formStatus: item.id });
  }

  onFormStatusDeselect(): void {
    this.formReportsForm.patchValue({ formStatus: '' });
  }

  onSpecificFormSelect(item: any): void {
    this.formReportsForm.patchValue({ specificForm: item.id });
  }

  onSpecificFormDeselect(): void {
    this.formReportsForm.patchValue({ specificForm: '' });
  }

  onSpecificFormFilterChange(searchText: string): void {
    if (!searchText) {
      this.filteredFormOptions = [...this.specificFormOptions];
    } else {
      // Filter and sort by relevance - exact matches first, then starts with, then contains
      const searchLower = searchText.toLowerCase();
      this.filteredFormOptions = this.specificFormOptions
        .filter(form => form.itemName.toLowerCase().includes(searchLower))
        .sort((a, b) => {
          const aName = a.itemName.toLowerCase();
          const bName = b.itemName.toLowerCase();

          // Exact match first
          if (aName === searchLower) return -1;
          if (bName === searchLower) return 1;

          // Starts with second
          if (aName.startsWith(searchLower) && !bName.startsWith(searchLower)) return -1;
          if (bName.startsWith(searchLower) && !aName.startsWith(searchLower)) return 1;

          // Alphabetical order for the rest
          return aName.localeCompare(bName);
        });
    }
  }

  getSiteIds(data: any): void {
    this.selectedSiteIds = data.siteId;
    this.formReportsForm.patchValue({ site: data.siteId });
  }

  hideDropdown(hideItem: any): void {
    this.hideSiteSelection = hideItem.hideItem;
  }

  onSubmit(): void {
    if (this.formReportsForm.valid) {
      this.isSubmitting = true;
      this.showDownloadButton = false;
      
      const formData = {
        ...this.formReportsForm.value,
        siteId: this.selectedSiteIds
      };
      
      // Simulate API call with static data - replace with actual API call when backend is ready
      setTimeout(() => {
        this.isSubmitting = false;

        // Generate a mock download URL with timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const formName = this.getSelectedFormName() || 'All-Forms';
        const fileName = `Form-Report-${formName}-${timestamp}.xlsx`;

        // Simulate successful report generation
        this.downloadUrl = `#mock-download-${Date.now()}`;
        this.showDownloadButton = true;

        this.structureService.notifyMessage({
          message: 'Report generated successfully! Click Download to get your report.',
          delay: 3000
        });
      }, 2000); // Simulate 2 second processing time
    } else {
      this.markFormGroupTouched();
    }
  }

  downloadReport(): void {
    if (this.downloadUrl) {
      try {
        // For static data demo, create a mock CSV content
        const formData = this.formReportsForm.value;
        const csvContent = this.generateMockCSVContent(formData);

        // Create blob and download
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const formName = this.getSelectedFormName() || 'All-Forms';
        const filename = `Form-Report-${formName}-${timestamp}.csv`;

        if (navigator.msSaveBlob) {
          // IE 10+
          navigator.msSaveBlob(blob, filename);
        } else {
          link.href = URL.createObjectURL(blob);
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }

        this.structureService.notifyMessage({
          message: 'Report downloaded successfully!',
          delay: 3000
        });
      } catch (error) {
        console.error('Error downloading report:', error);
        this.structureService.notifyMessage({
          message: 'Error downloading report. Please try again.',
          delay: 3000
        });
      }
    }
  }

  getSelectedFormName(): string {
    const control = this.formReportsForm.get('specificForm');
    if (control && control.value) {
      const formId = control.value;
      const form = this.specificFormOptions.find(f => f.id === formId);
      if (form) {
        return form.itemName.replace(/\s+/g, '-');
      }
    }
    return '';
  }

  generateMockCSVContent(formData: any): string {
    // Generate mock CSV content based on form criteria
    const headers = ['Patient ID', 'Patient Name', 'Form Name', 'Status', 'Submission Date', 'Site', 'Clinician'];
    const mockData = [
      ['P001', 'John Doe', 'Patient Registration Form', 'Completed', '2024-01-15', 'Main Clinic', 'Dr. Smith'],
      ['P002', 'Jane Smith', 'Medical History Form', 'Completed', '2024-01-16', 'Main Clinic', 'Dr. Johnson'],
      ['P003', 'Bob Wilson', 'Consent Form', 'Pending', '2024-01-17', 'Branch Clinic', 'Dr. Brown'],
      ['P004', 'Alice Johnson', 'Insurance Information Form', 'Completed', '2024-01-18', 'Main Clinic', 'Dr. Davis'],
      ['P005', 'Charlie Brown', 'Emergency Contact Form', 'Completed', '2024-01-19', 'Branch Clinic', 'Dr. Wilson']
    ];

    let csvContent = headers.join(',') + '\n';
    mockData.forEach(row => {
      csvContent += row.map(field => `"${field}"`).join(',') + '\n';
    });

    return csvContent;
  }

  resetForm(): void {
    this.formReportsForm.reset();
    this.formReportsForm.patchValue({ missingRecords: false });
    this.showDownloadButton = false;
    this.downloadUrl = '';
    this.eventsSubject.next('reset');
  }



  private markFormGroupTouched(): void {
    Object.keys(this.formReportsForm.controls).forEach(key => {
      const control = this.formReportsForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }
}
