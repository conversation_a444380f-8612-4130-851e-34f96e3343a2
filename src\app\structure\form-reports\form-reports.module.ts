import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { TranslateModule } from '@ngx-translate/core';

import { FormReportsComponent } from './form-reports.component';
import { SharedModule } from '../shared/sharedModule';
import { AuthGuard } from '../../guard/auth.guard';

export const routes: Routes = [
  { 
    path: 'form-reports', 
    component: FormReportsComponent, 
    canActivate: [AuthGuard],
    data: { checkRoutingPrivileges: 'showFormReports' }
  },
];

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    AngularMultiSelectModule,
    SharedModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    RouterModule
  ],
  declarations: [
    FormReportsComponent
  ],
  exports: [
    TranslateModule
  ]
})
export class FormReportsModule { }
